<!DOCTYPE html>
<html>
<head>
    <title>Test Product Selection</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
</head>
<body>
    <h1>Product Selection Test</h1>
    <p>Press space key in the input field below to see all products:</p>
    <input type="text" id="lims_productcodeSearch" placeholder="Press space to see products..." style="width: 300px; padding: 10px;">
    
    <div id="debug" style="margin-top: 20px; padding: 10px; background: #f0f0f0;">
        <h3>Debug Info:</h3>
        <div id="debug-content"></div>
    </div>

    <script>
        // Test data
        var lims_product_array = [
            'PROD001|Product 1||0',
            'PROD002|Product 2||0',
            'PROD003|Product 3||0',
            'PROD004|Product 4||0',
            'PROD005|Product 5||0'
        ];
        
        function debugLog(message) {
            $('#debug-content').append('<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>');
        }
        
        $(document).ready(function() {
            debugLog('Document ready, product array has ' + lims_product_array.length + ' items');
            
            var lims_productcodeSearch = $('#lims_productcodeSearch');
            
            lims_productcodeSearch.autocomplete({
                source: function(request, response) {
                    debugLog('Autocomplete source called with term: "' + request.term + '"');
                    debugLog('Product array available: ' + lims_product_array.length + ' items');
                    var matcher = new RegExp(".?" + $.ui.autocomplete.escapeRegex(request.term), "i");
                    var results = $.grep(lims_product_array, function(item) {
                        return matcher.test(item);
                    });
                    debugLog('Filtered results: ' + results.length + ' items');
                    response(results);
                },
                response: function(event, ui) {
                    debugLog('Autocomplete response: ' + ui.content.length + ' items returned');
                },
                select: function(event, ui) {
                    debugLog('Item selected: ' + ui.item.value);
                }
            });
            
            // Show all products only when space key is pressed
            lims_productcodeSearch.on('keydown', function(e) {
                if (e.which == 32) { // Space key
                    debugLog('Space key pressed!');
                    e.preventDefault(); // Prevent space from being typed
                    $(this).autocomplete('search', '');
                }
            });
            
            debugLog('Autocomplete initialized');
        });
    </script>
</body>
</html>

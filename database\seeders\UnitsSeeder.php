<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Unit;

class UnitsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if units already exist
        if (Unit::count() > 0) {
            echo "Units already exist. Skipping seeder.\n";
            return;
        }

        // Base units (no conversion)
        $baseUnits = [
            [
                'unit_code' => 'pc',
                'unit_name' => 'Piece',
                'base_unit' => null,
                'operator' => null,
                'operation_value' => null,
                'is_active' => true,
            ],
            [
                'unit_code' => 'kg',
                'unit_name' => 'Kilogram',
                'base_unit' => null,
                'operator' => null,
                'operation_value' => null,
                'is_active' => true,
            ],
            [
                'unit_code' => 'ltr',
                'unit_name' => 'Liter',
                'base_unit' => null,
                'operator' => null,
                'operation_value' => null,
                'is_active' => true,
            ],
            [
                'unit_code' => 'mtr',
                'unit_name' => 'Meter',
                'base_unit' => null,
                'operator' => null,
                'operation_value' => null,
                'is_active' => true,
            ],
        ];

        foreach ($baseUnits as $unit) {
            Unit::create($unit);
        }

        // Get the created base units for sub-units
        $pieceUnit = Unit::where('unit_code', 'pc')->first();
        $kgUnit = Unit::where('unit_code', 'kg')->first();
        $ltrUnit = Unit::where('unit_code', 'ltr')->first();
        $mtrUnit = Unit::where('unit_code', 'mtr')->first();

        // Sub-units (with conversion)
        $subUnits = [
            // Piece sub-units
            [
                'unit_code' => 'box',
                'unit_name' => 'Box',
                'base_unit' => $pieceUnit->id,
                'operator' => '*',
                'operation_value' => 12,
                'is_active' => true,
            ],
            [
                'unit_code' => 'dozen',
                'unit_name' => 'Dozen',
                'base_unit' => $pieceUnit->id,
                'operator' => '*',
                'operation_value' => 12,
                'is_active' => true,
            ],
            // Weight sub-units
            [
                'unit_code' => 'gm',
                'unit_name' => 'Gram',
                'base_unit' => $kgUnit->id,
                'operator' => '/',
                'operation_value' => 1000,
                'is_active' => true,
            ],
            [
                'unit_code' => 'ton',
                'unit_name' => 'Ton',
                'base_unit' => $kgUnit->id,
                'operator' => '*',
                'operation_value' => 1000,
                'is_active' => true,
            ],
            // Volume sub-units
            [
                'unit_code' => 'ml',
                'unit_name' => 'Milliliter',
                'base_unit' => $ltrUnit->id,
                'operator' => '/',
                'operation_value' => 1000,
                'is_active' => true,
            ],
            // Length sub-units
            [
                'unit_code' => 'cm',
                'unit_name' => 'Centimeter',
                'base_unit' => $mtrUnit->id,
                'operator' => '/',
                'operation_value' => 100,
                'is_active' => true,
            ],
            [
                'unit_code' => 'mm',
                'unit_name' => 'Millimeter',
                'base_unit' => $mtrUnit->id,
                'operator' => '/',
                'operation_value' => 1000,
                'is_active' => true,
            ],
        ];

        foreach ($subUnits as $unit) {
            Unit::create($unit);
        }

        echo "Units seeded successfully!\n";
    }
}

<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

// Testing Purpose

class DailyQuote extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'quote:daily';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Respectively send an exclusive quote to everyone daily via email.';
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $quotes = [
            '<PERSON><PERSON><PERSON> Gandhi' => 'Live as if you were to die tomorrow. Learn as if you were to live forever.',
            '<PERSON>' => 'That which does not kill us makes us stronger.',
            '<PERSON>' => 'Do what you can, with what you have, where you are.',
            '<PERSON>' => 'Be yourself; everyone else is already taken.',
            '<PERSON>' => 'This above all: to thine own self be true.',
            '<PERSON> Hill' => 'If you cannot do great things, do small things in a great way.',
            '<PERSON>' => 'If opportunity doesn’t knock, build a door.'
        ];

        // Setting up a random word
        $key = array_rand($quotes);
        $data = $quotes[$key];

        $user = User::find(1);
        Mail::raw("{$key} -> {$data}", function ($mail) use ($user) {
            $mail->from('<EMAIL>');
            $mail->to($user->email)
                ->subject('Daily New Quote!');
        });

        $this->info('Successfully sent daily quote to everyone.');
    }
}
